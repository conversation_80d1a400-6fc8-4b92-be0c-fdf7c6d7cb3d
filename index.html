<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر الصور - تغيير الألوان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .toolbar {
            background: #f5f5f5;
            padding: 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            justify-content: center;
        }

        .tool-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .tool-group label {
            font-weight: bold;
            color: #333;
            font-size: 0.9em;
        }

        input[type="file"] {
            padding: 10px;
            border: 2px dashed #4CAF50;
            border-radius: 8px;
            background: white;
            cursor: pointer;
        }

        input[type="range"] {
            width: 150px;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
        }

        input[type="color"] {
            width: 60px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .workspace {
            padding: 30px;
            text-align: center;
            min-height: 500px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        #canvas {
            border: 3px solid #4CAF50;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 100%;
            height: auto;
        }

        .upload-area {
            border: 3px dashed #4CAF50;
            border-radius: 15px;
            padding: 40px;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background: #f0f8f0;
            border-color: #45a049;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }

        .instructions {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px;
            border-right: 5px solid #4CAF50;
        }

        .instructions h3 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .instructions ul {
            color: #555;
            text-align: right;
        }

        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 محرر الصور المتقدم</h1>
            <p>غيّر ألوان صورك بسهولة وإبداع</p>
        </div>

        <div class="toolbar">
            <div class="tool-group">
                <label>اختر صورة:</label>
                <input type="file" id="imageInput" accept="image/*">
            </div>

            <div class="tool-group">
                <label>اللون الجديد:</label>
                <input type="color" id="colorPicker" value="#00ff00">
            </div>

            <div class="tool-group">
                <label>شدة التأثير:</label>
                <input type="range" id="intensitySlider" min="0" max="100" value="80">
                <span id="intensityValue">80%</span>
            </div>

            <div class="tool-group">
                <label>حساسية اللون:</label>
                <input type="range" id="toleranceSlider" min="1" max="100" value="30">
                <span id="toleranceValue">30</span>
            </div>

            <button onclick="applyColorChange()">🎨 تطبيق التغيير</button>
            <button onclick="resetImage()">↺ إعادة تعيين</button>
            <button onclick="downloadImage()">💾 تحميل الصورة</button>
        </div>

        <div class="workspace">
            <div id="uploadArea" class="upload-area">
                <div class="upload-text">📸 اسحب صورة الدب هنا أو اضغط لاختيار صورة</div>
                <input type="file" id="dragInput" accept="image/*" style="display: none;">
            </div>
            <canvas id="canvas" style="display: none;"></canvas>
        </div>

        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ul>
                <li>ارفع صورة الدب الأبيض</li>
                <li>اختر اللون الأخضر من منتقي الألوان</li>
                <li>اضبط شدة التأثير وحساسية اللون</li>
                <li>اضغط "تطبيق التغيير" لتحويل لون الدب</li>
                <li>احفظ النتيجة بالضغط على "تحميل الصورة"</li>
            </ul>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
